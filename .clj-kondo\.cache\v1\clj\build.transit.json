["^ ", "~$get-project-aliases", ["^ ", "~:fixed-arities", ["~#set", [0]], "~:private", true, "~:ns", "~$build", "~:name", "^0", "~:type", "~:fn", "~:col", 1, "~:top-ns", "^5", "~:row", 14], "~$ensure-project-root", ["^ ", "^1", ["^2", [2]], "^3", true, "^4", "^5", "^6", "^<", "^7", "^8", "^9", 1, "^:", "^5", "~:arities", ["^ ", "~i2", ["^ ", "~:ret", "~:string", "~:arglist-str", "[task project]"]], "^;", 21], "~$uberjar", ["^ ", "^;", 32, "^9", 1, "^1", ["^2", [1]], "^6", "^A", "^4", "^5", "^:", "^5", "^7", "^8"], "~:filename", "C:\\Users\\<USER>\\gitProjects\\clojure-polylith-realworld-example-app\\build.clj"]