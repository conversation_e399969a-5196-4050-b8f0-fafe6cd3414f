<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.example.AppTest" time="0.083" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="22"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\Users\<USER>\mavenProjects\java-clojure-project\target\test-classes;C:\Users\<USER>\mavenProjects\java-clojure-project\target\classes;C:\Users\<USER>\.m2\repository\junit\junit\3.8.1\junit-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\clojure\clojure\1.11.1\clojure-1.11.1.jar;C:\Users\<USER>\.m2\repository\org\clojure\spec.alpha\0.3.218\spec.alpha-0.3.218.jar;C:\Users\<USER>\.m2\repository\org\clojure\core.specs.alpha\0.2.62\core.specs.alpha-0.2.62.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="America/New_York"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="22"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="c:\Users\<USER>\Downloads\jdk-22.0.1\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire5072473970887250501\surefirebooter-20250627115936797_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire5072473970887250501 2025-06-27T11-59-36_487-jvmRun1 surefire-20250627115936797_1tmp surefire_0-20250627115936797_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\mavenProjects\java-clojure-project\target\test-classes;C:\Users\<USER>\mavenProjects\java-clojure-project\target\classes;C:\Users\<USER>\.m2\repository\junit\junit\3.8.1\junit-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\clojure\clojure\1.11.1\clojure-1.11.1.jar;C:\Users\<USER>\.m2\repository\org\clojure\spec.alpha\0.3.218\spec.alpha-0.3.218.jar;C:\Users\<USER>\.m2\repository\org\clojure\core.specs.alpha\0.2.62\core.specs.alpha-0.2.62.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Users\jtcum\Downloads\jdk-22.0.1"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\mavenProjects\java-clojure-project"/>
    <property name="java.vm.compressedOopsMode" value="32-bit"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire5072473970887250501\surefirebooter-20250627115936797_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="22.0.1+8-16"/>
    <property name="user.name" value="jtcum"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="22.0.1"/>
    <property name="user.dir" value="C:\Users\<USER>\mavenProjects\java-clojure-project"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="c:\Users\<USER>\Downloads\jdk-22.0.1\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\PowerShell\7;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\PuTTY\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\tools\groovy-3.0.21\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PowerShell\7\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Apps\clojure\;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;c:\Users\<USER>\bin;c:\Users\<USER>\Downloads\jdk-22.0.1\bin;C:\Program Files\Polylith;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="22.0.1+8-16"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="66.0"/>
  </properties>
  <testcase name="testApp" classname="com.example.AppTest" time="0.016"/>
</testsuite>