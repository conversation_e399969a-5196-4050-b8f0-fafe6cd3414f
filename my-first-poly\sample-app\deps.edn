{:aliases {:dev {:extra-paths ["development/src"]
                 :extra-deps {; Components
                              poly/database {:local/root "components/database"}
                              poly/user {:local/root "components/user"}
                              poly/config {:local/root "components/config"}
 
                              ; Bases
                              poly/api {:local/root "bases/api"}
 
                              ; Dev dependencies
                              org.clojure/clojure {:mvn/version "1.11.1"}}}
 
           :test {:extra-paths ["components/database/test"
                                "components/user/test"
                                "components/config/test"
                                "bases/api/test"]}
 
           :poly {:main-opts ["-m" "polylith.clj.core.poly-cli.core"]
                  :extra-deps {polylith/clj-poly {:mvn/version "0.2.18-SNAPSHOT"}}}}}
