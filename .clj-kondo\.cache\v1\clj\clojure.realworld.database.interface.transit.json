["^ ", "~$db", ["^ ", "~:row", 5, "~:col", 1, "~:fixed-arities", ["~#set", [0, 1]], "~:name", "^0", "~:ns", "~$clojure.realworld.database.interface", "~:top-ns", "^7", "~:arities", ["^ ", "~i1", ["^ ", "~:ret", ["^ ", "~:type", "~:map", "~:val", ["^ ", "~:classname", ["^ ", "^1", 12, "~:end-row", 12, "^2", 18, "~:end-col", 35, "~:tag", "~:string"], "~:subprotocol", ["^ ", "^1", 13, "^?", 13, "^2", 18, "^@", 26, "^A", "^B"], "~:subname", ["^ ", "^1", 14, "^?", 14, "^2", 18, "^@", 22]]], "~:arglist-str", "[path]"], "~i0", ["^ ", "^:", ["^ ", "^;", "^<", "^=", ["^ ", "^>", ["^ ", "^1", 12, "^?", 12, "^2", 18, "^@", 35, "^A", "^B"], "^C", ["^ ", "^1", 13, "^?", 13, "^2", 18, "^@", 26, "^A", "^B"], "^D", ["^ ", "^1", 14, "^?", 14, "^2", 18, "^@", 22]]], "^E", "[]"]], "^;", "~:fn"], "~$db-exists?", ["^ ", "^1", 11, "^2", 1, "^3", ["^4", [0]], "^5", "^G", "^6", "^7", "^8", "^7", "^;", "^F"], "~$generate-db", ["^ ", "^1", 14, "^2", 1, "^3", ["^4", [1]], "^5", "^H", "^6", "^7", "^8", "^7", "^;", "^F"], "~$drop-db", ["^ ", "^1", 17, "^2", 1, "^3", ["^4", [1]], "^5", "^I", "^6", "^7", "^8", "^7", "^;", "^F"], "~$valid-schema?", ["^ ", "^1", 20, "^2", 1, "^3", ["^4", [1]], "^5", "^J", "^6", "^7", "^8", "^7", "^;", "^F"], "~:filename", "C:\\Users\\<USER>\\gitProjects\\clojure-polylith-realworld-example-app\\components\\database\\src\\clojure\\realworld\\database\\interface.clj"]