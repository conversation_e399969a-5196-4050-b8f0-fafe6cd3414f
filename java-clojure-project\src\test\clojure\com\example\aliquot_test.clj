(ns com.example.aliquot-test
  (:require [clojure.test :refer :all]
            [com.example.aliquot :refer :all]))

(deftest test-aliquot-step
  (testing "Testing aliquot-step function"
    (is (= 0 (aliquot-step 0)))
    (is (= 0 (aliquot-step 1)))
    (is (= 1 (aliquot-step 2)))
    (is (= 1 (aliquot-step 3)))
    (is (= 3 (aliquot-step 4)))
    (is (= 1 (aliquot-step 5)))
    (is (= 6 (aliquot-step 6)))
    (is (= 1 (aliquot-step 7)))
    (is (= 7 (aliquot-step 8)))
    (is (= 4 (aliquot-step 9)))
    (is (= 8 (aliquot-step 10)))
    (is (= 1 (aliquot-step 11)))
    (is (= 16 (aliquot-step 12)))))

(deftest test-aliquot-series
  (testing "Testing aliquot-series function"
    (is (= [6] (aliquot-series 6)))
    (is (= [12 16 15 9 4 3 1] (aliquot-series 12)))
    (is (= [28] (aliquot-series 28)))))

(deftest test-amicable-pairs
  (testing "Testing amicable-pairs function"
    (is (= [] (amicable-pairs 200)))
    (is (= [[220 284]] (amicable-pairs 300)))
    (is (= [[220 284] [1184 1210]] (amicable-pairs 1300)))
    (is (= [[220 284] [1184 1210] [2620 2924]] (amicable-pairs 3000)))))

(deftest test-main
  (testing "Testing -main function with default value"
    (let [output (with-out-str (-main))]
      (is (= "([220 284] [1184 1210] [2620 2924] [5020 5564] [6232 6368])\r\n" output))))
  
  (testing "Testing -main function with argument"
    (let [output (with-out-str (-main "300"))]
      (is (= "([220 284])\r\n" output)))))


