["^ ", "~$keywordize", ["^ ", "~:row", 6, "~:col", 1, "~:fixed-arities", ["~#set", [1]], "~:name", "^0", "~:ns", "~$clojure.realworld.env.core", "~:top-ns", "^7", "~:type", "~:fn"], "~$read-system-env", ["^ ", "^1", 13, "^2", 1, "^3", ["^4", [0]], "^5", "^;", "^6", "^7", "^8", "^7", "^9", "^:"], "~$read-system-props", ["^ ", "^1", 18, "^2", 1, "^3", ["^4", [0]], "^5", "^<", "^6", "^7", "^8", "^7", "^9", "^:"], "~$slurp-file", ["^ ", "^1", 23, "^2", 1, "^3", ["^4", [1]], "^5", "^=", "^6", "^7", "^8", "^7", "^9", "^:"], "~$read-env-file", ["^ ", "^1", 28, "^2", 1, "^3", ["^4", [1]], "^5", "^>", "^6", "^7", "^8", "^7", "^9", "^:"], "~$env", ["^ ", "^1", 38, "^2", 1, "^5", "^?", "^6", "^7", "^8", "^7", "^9", ["^ ", "~:tag", "~:nilable/map", "^1", 42, "^2", 5, "~:end-row", 44, "~:end-col", 21]], "~:filename", "C:\\Users\\<USER>\\gitProjects\\clojure-polylith-realworld-example-app\\components\\env\\src\\clojure\\realworld\\env\\core.clj"]