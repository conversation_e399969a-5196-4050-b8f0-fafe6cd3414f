(ns com.example.aliquot
  (:gen-class))

(defn aliquot-step [n]
  (if (<= n 1)
    0
    (->> (range 1 n)
         (filter #(zero? (mod n %)))
         (reduce +))))

(defn aliquot-series [n]
  (distinct (take 100 (take-while pos? (iterate aliquot-step n)))))

(defn amicable-pairs [n]
  (for [a (range 1 (inc n))
        :let [b (aliquot-step a)]
        :when (and (> b a)
                   (<= b n)
                   (= a (aliquot-step b)))]
    [a b]))

(defn -main
  [& args]
  (let [n (if (seq args)
            (Integer/parseInt (first args))
            10000)]
    (println (amicable-pairs n))))
