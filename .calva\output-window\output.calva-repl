; This is the Calva REPL Window.
; It's just a file, really, with some special treatment from Calva.
; Use it as a REPL input prompt if you like. (When the REPL is connected.)
; TIPS: The keyboard shortcut `ctrl+alt+o r` shows and focuses this window
; Please see https://calva.io/repl-window/ for more info.
; Happy coding! ♥️

; TIPS: As with any Clojure file when the REPL is connected:
; - `alt+enter` evaluates the current top level form.
; - `ctrl+enter` evaluates the current form.
; Special for this file:
; - `alt+up` and `alt+down` traverse up and down the REPL command history
;    when the cursor is after the last contents at the prompt
; 
; See also the Calva Inspector: https://calva.io/inspector/

PLEASE NOTE
We will update the default location of this file.
The new default location will be
  "<projectRootPath>/.calva/repl.calva-repl"
For now the legacy path is used by default.
To give yourself a smooth transition, you can opt in
to the change, by configuring this setting as false:
  "calva.useLegacyReplWindowPath"
and then add "**/.calva/repl.calva-repl" to your ".gitignore" file.


This file is configured as the output destination for all REPL output.
You can configure this with the setting:
  "calva.outputDestinations"


; Jacking in...
