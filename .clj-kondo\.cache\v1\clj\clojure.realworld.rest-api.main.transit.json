["^ ", "~$server-ref", ["^ ", "~:row", 7, "~:col", 1, "~:private", true, "~:name", "^0", "~:ns", "~$clojure.realworld.rest-api.main", "~:top-ns", "^6", "~:type", "~:atom"], "~$start!", ["^ ", "^1", 9, "^2", 1, "~:fixed-arities", ["~#set", [1]], "^4", "^:", "^5", "^6", "^7", "^6", "^8", "~:fn"], "~$stop!", ["^ ", "^1", 21, "^2", 1, "^;", ["^<", [0]], "^4", "^>", "^5", "^6", "^7", "^6", "^8", "^="], "~$-main", ["^ ", "^1", 28, "^2", 1, "~:varargs-min-arity", 0, "^4", "^?", "^5", "^6", "^7", "^6", "^8", "^="], "~:filename", "C:\\Users\\<USER>\\gitProjects\\clojure-polylith-realworld-example-app\\bases\\rest-api\\src\\clojure\\realworld\\rest_api\\main.clj"]