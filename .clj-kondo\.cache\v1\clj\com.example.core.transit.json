["^ ", "~$hello", ["^ ", "~:row", 4, "~:col", 1, "~:fixed-arities", ["~#set", [0]], "~:name", "^0", "~:ns", "~$com.example.core", "~:top-ns", "^7", "~:arities", ["^ ", "~i0", ["^ ", "~:ret", "~:string", "~:arglist-str", "[]"]], "~:type", "~:fn"], "~$-main", ["^ ", "^1", 7, "^2", 1, "~:varargs-min-arity", 0, "^5", "^?", "^6", "^7", "^8", "^7", "^=", "^>"], "~:filename", "C:\\Users\\<USER>\\mavenProjects\\java-clojure-project\\src\\main\\clojure\\com\\example\\core.clj"]