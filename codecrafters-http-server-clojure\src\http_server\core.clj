(ns http-server.core
  (:require
   [clojure.java.io :as io])
  (:import
   [java.net ServerSocket])
  (:gen-class))

(def port 4221)

(defn serve [] 
  (with-open [server-sock (ServerSocket. port)
              sock        (.accept server-sock)]
    (let [msg-out "HTTP/1.1 200 OK\r\n\r\n"] 
      (with-open [w (io/writer sock)]
        (.write w msg-out)
        (.flush w)))))

(defn -main [& args]
  ;; You can use print statements as follows for debugging, they'll be visible when running tests.
  (println "Logs from your program will appear here!")

  ;; Uncomment this block to pass the first stage
  (serve)

  )
