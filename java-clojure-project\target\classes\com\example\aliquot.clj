(ns com.example.aliquot
  (:gen-class))

(defn aliquot-step [n]
  (if (<= n 1)
    0
    (->> (range 1 n)
         (filter #(zero? (mod n %)))
         (reduce +))))

(defn aliquot-series [n]
  (distinct (take 100 (take-while pos? (iterate aliquot-step n)))))

(defn amicable-pairs [n]
  (for [a (range 1 (inc n))
        :let [b (aliquot-step a)]
        :when (and (> b a)
                   (<= b n)
                   (= a (aliquot-step b)))]
    [a b]))

(defn -main
  [& args]
  (let [command (first args)
        n (if (> (count args) 1)
            (Integer/parseInt (second args))
            10000)]
    (case command
      "aliquot-series" (println (aliquot-series n))
      "amicable-pairs" (println (amicable-pairs n))
      (do
        (println "Commands:")
        (println "  aliquot-series  - Generate aliquot series for the given number")
        (println "  amicable-pairs  - Find amicable pairs up to the given number")))))
