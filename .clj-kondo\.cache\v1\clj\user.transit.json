["^ ", "~$proper-divisors", ["^ ", "~:row", 4, "~:col", 1, "~:fixed-arities", ["~#set", [1]], "~:name", "^0", "~:ns", "~$user", "~:top-ns", null, "~:arities", ["^ ", "~i1", ["^ ", "~:ret", "~:seq", "~:arglist-str", "[n]"]], "~:type", "~:fn"], "~$aliquot-sequence", ["^ ", "^1", 6, "^2", 1, "^3", ["^4", [1]], "^5", "^?", "^6", "^7", "^8", null, "^9", ["^ ", "~i1", ["^ ", "^:", "^;", "^<", "[n]"]], "^=", "^>"], "~$is-perfect?", ["^ ", "^1", 13, "^2", 1, "^3", ["^4", [1]], "^5", "^@", "^6", "^7", "^8", null, "^9", ["^ ", "~i1", ["^ ", "^:", "~:boolean", "^<", "[n]"]], "^=", "^>"], "~$aliquot-step", ["^ ", "^1", 22, "^2", 1, "^3", ["^4", [1]], "^5", "^B", "^6", "^7", "^8", null, "^=", "^>"], "~$take-aliquot-sequence", ["^ ", "^1", 26, "^2", 1, "^3", ["^4", [1]], "^5", "^C", "^6", "^7", "^8", null, "^=", "^>"], "~$aliquot-sequence-for", ["^ ", "^1", 29, "^2", 1, "^3", ["^4", [1]], "^5", "^D", "^6", "^7", "^8", null, "^=", "^>"], "~$memoized-proper-divisors", ["^ ", "^1", 146, "^2", 1, "^5", "^E", "^6", "^7", "^8", null], "~:filename", "C:\\Users\\<USER>\\leinProjects\\my-demo2\\.lein-repl-history"]