(ns com.example.shor
  (:require [clojure.math.numeric-tower :as math])
  (:gen-class))

(defn gcd [a b]
  (if (zero? b) a (recur b (mod a b))))

(defn mod-exp [base exp mod]
  (loop [result 1, b base, e exp]
    (if (zero? e)
      result
      (recur
       (if (odd? e) (mod (* result b) mod) result)
       (mod (* b b) mod)
       (quot e 2)))))

(defn find-period [a n]
  (loop [r 1]
    (if (= 1 (mod-exp a r n))
      r
      (recur (inc r)))))

(defn shor [n]
  (if (even? n)
    [2 (/ n 2)]
    (loop []
      (let [a (+ 2 (rand-int (- n 3)))
            g (gcd a n)]
        (if (not= g 1)
          [g (/ n g)]
          (let [r (find-period a n)]
            (if (and (even? r)
                     (not= (mod-exp a (/ r 2) n) n)
                     (not= (mod-exp a (/ r 2) n) 1))
              (let [x (mod-exp a (/ r 2) n)
                    p (gcd (- x 1) n)
                    q (gcd (+ x 1) n)]
                (if (and (> p 1) (> q 1))
                  [p q]
                  (recur)))
              (recur))))))))

(defn -main
  [& args]
  (println (shor (Integer/parseInt (first args)))))