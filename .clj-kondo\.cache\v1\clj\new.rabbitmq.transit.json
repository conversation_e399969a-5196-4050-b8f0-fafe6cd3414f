["^ ", "~$connection-factory", ["^ ", "~:row", 12, "~:col", 1, "~:name", "^0", "~:ns", "~$new.rabbitmq", "~:top-ns", "^5"], "~$connection", ["^ ", "^1", 17, "^2", 1, "^3", "^7", "^4", "^5", "^6", "^5"], "~$channel", ["^ ", "^1", 20, "^2", 1, "^3", "^8", "^4", "^5", "^6", "^5"], "~$send-hello", ["^ ", "^1", 27, "^2", 1, "~:fixed-arities", ["~#set", [2]], "^3", "^9", "^4", "^5", "^6", "^5", "~:type", "~:fn"], "~$consumer", ["^ ", "^1", 39, "^2", 1, "^3", "^>", "^4", "^5", "^6", "^5"], "~$recv-hello", ["^ ", "^1", 45, "^2", 1, "^:", ["^;", [2]], "^3", "^?", "^4", "^5", "^6", "^5", "^<", "^="], "~:filename", "C:\\Users\\<USER>\\gorilla-test-present\\src\\new\\rabbitmq.clj"]