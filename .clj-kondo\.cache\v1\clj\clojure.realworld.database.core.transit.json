["^ ", "~$db-path", ["^ ", "~:fixed-arities", ["~#set", [0]], "~:private", true, "~:ns", "~$clojure.realworld.database.core", "~:name", "^0", "~:type", "~:fn", "~:col", 1, "~:top-ns", "^5", "~:row", 5], "~$db", ["^ ", "^;", 10, "^9", 1, "^1", ["^2", [0, 1]], "^6", "^<", "^4", "^5", "^:", "^5", "~:arities", ["^ ", "~i1", ["^ ", "~:ret", ["^ ", "^7", "~:map", "~:val", ["^ ", "~:classname", ["^ ", "^;", 12, "~:end-row", 12, "^9", 18, "~:end-col", 35, "~:tag", "~:string"], "~:subprotocol", ["^ ", "^;", 13, "^B", 13, "^9", 18, "^C", 26, "^D", "^E"], "~:subname", ["^ ", "^;", 14, "^B", 14, "^9", 18, "^C", 22]]], "~:arglist-str", "[path]"], "~i0", ["^ ", "^>", ["^ ", "^7", "^?", "^@", ["^ ", "^A", ["^ ", "^;", 12, "^B", 12, "^9", 18, "^C", 35, "^D", "^E"], "^F", ["^ ", "^;", 13, "^B", 13, "^9", 18, "^C", 26, "^D", "^E"], "^G", ["^ ", "^;", 14, "^B", 14, "^9", 18, "^C", 22]]], "^H", "[]"]], "^7", "^8"], "~$db-exists?", ["^ ", "^;", 18, "^9", 1, "^1", ["^2", [0]], "^6", "^I", "^4", "^5", "^:", "^5", "^7", "^8"], "~:filename", "C:\\Users\\<USER>\\gitProjects\\clojure-polylith-realworld-example-app\\components\\database\\src\\clojure\\realworld\\database\\core.clj"]