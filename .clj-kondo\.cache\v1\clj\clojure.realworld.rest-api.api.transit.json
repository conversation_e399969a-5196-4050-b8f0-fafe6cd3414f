["^ ", "~$public-routes", ["^ ", "~:row", 14, "~:col", 1, "~:name", "^0", "~:ns", "~$clojure.realworld.rest-api.api", "~:top-ns", "^5"], "~$private-routes", ["^ ", "^1", 25, "^2", 1, "^3", "^7", "^4", "^5", "^6", "^5"], "~$other-routes", ["^ ", "^1", 39, "^2", 1, "^3", "^8", "^4", "^5", "^6", "^5"], "~$app-routes", ["^ ", "^1", 42, "^2", 1, "~:private", true, "^3", "^9", "^4", "^5", "^6", "^5"], "~$app", ["^ ", "^1", 51, "^2", 1, "^3", "^;", "^4", "^5", "^6", "^5"], "~$init", ["^ ", "^1", 63, "^2", 1, "~:fixed-arities", ["~#set", [0]], "^3", "^<", "^4", "^5", "^6", "^5", "~:type", "~:fn"], "~$destroy", ["^ ", "^1", 81, "^2", 1, "^=", ["^>", [0]], "^3", "^A", "^4", "^5", "^6", "^5", "^?", "^@"], "~:filename", "C:\\Users\\<USER>\\gitProjects\\clojure-polylith-realworld-example-app\\bases\\rest-api\\src\\clojure\\realworld\\rest_api\\api.clj"]